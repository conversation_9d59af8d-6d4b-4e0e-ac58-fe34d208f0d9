import { auth } from '@/auth';
import { db } from '@/lib/db';
import { NextRequest, NextResponse } from 'next/server';
import { ATTPANEL_CONFIG } from './attpanel/route';
import { GROWFOLLOWS_CONFIG } from './growfollows/route';
import { SMMCODER_CONFIG } from './smmcoder/route';
import { SMMGEN_CONFIG } from './smmgen/route';

// Available Providers Configuration
// Remove any provider from here = it won't show in UI
export const AVAILABLE_PROVIDERS = [
  SMMGEN_CONFIG,
  GROWFOLLOWS_CONFIG,
  ATTPANEL_CONFIG,
  SMMCODER_CONFIG
];

// GET - Get all available providers
export async function GET() {
  try {
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        {
          error: 'Unauthorized access. Admin privileges required.',
          success: false,
          data: null
        },
        { status: 401 }
      );
    }

    // Get configured providers from database
    let configuredProviders: any[] = [];
    try {
      // First ensure api_providers table exists and rename if needed
      await db.$executeRaw`
        CREATE TABLE IF NOT EXISTS \`api_providers\` (
          \`id\` int NOT NULL AUTO_INCREMENT,
          \`name\` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
          \`api_key\` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
          \`login_user\` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
          \`login_pass\` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
          \`status\` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inactive',
          \`createdAt\` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          \`updatedAt\` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          PRIMARY KEY (\`id\`),
          UNIQUE KEY \`api_providers_name_key\` (\`name\`),
          KEY \`api_providers_status_idx\` (\`status\`),
          KEY \`api_providers_name_idx\` (\`name\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;



      configuredProviders = await db.apiProvider.findMany({
        select: {
          id: true,
          name: true,
          api_url: true,
          status: true,
          createdAt: true,
          updatedAt: true
        }
      });
    } catch (error) {
      console.log('Provider table error:', error);
      configuredProviders = [];
    }

    // Merge available providers with configured status
    const providersWithStatus = AVAILABLE_PROVIDERS.map(provider => {
      const configured = configuredProviders.find((cp: any) => cp.name === provider.value);
      return {
        ...provider,
        configured: !!configured,
        status: configured?.status || 'inactive',
        id: configured?.id || null,
        apiUrl: configured?.api_url || provider.apiUrl || '',
        createdAt: configured?.createdAt || null,
        updatedAt: configured?.updatedAt || null
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        providers: providersWithStatus,
        total: providersWithStatus.length,
        configured: configuredProviders.length,
        available: AVAILABLE_PROVIDERS.length
      },
      error: null
    });

  } catch (error) {
    console.error('Error getting providers:', error);
    return NextResponse.json(
      {
        error: 'Failed to get providers: ' + (error instanceof Error ? error.message : 'Unknown error'),
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

// POST - Add new provider
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        {
          error: 'Unauthorized access. Admin privileges required.',
          success: false,
          data: null
        },
        { status: 401 }
      );
    }

    const { selectedProvider, apiKey, apiUrl, username, password } = await req.json();

    if (!selectedProvider || !apiKey) {
      return NextResponse.json(
        {
          error: 'Provider and API key are required',
          success: false,
          data: null
        },
        { status: 400 }
      );
    }

    // Check if provider is available
    const providerConfig = AVAILABLE_PROVIDERS.find(p => p.value === selectedProvider);
    if (!providerConfig) {
      return NextResponse.json(
        {
          error: 'Invalid provider selected',
          success: false,
          data: null
        },
        { status: 400 }
      );
    }

    // Check if provider already exists and create new provider
    let newProvider: any;
    try {
      // Check if provider already exists
      const existingProvider = await db.apiProvider.findUnique({
        where: { name: selectedProvider }
      });

      if (existingProvider) {
        return NextResponse.json(
          {
            error: 'Provider already exists',
            success: false,
            data: null
          },
          { status: 400 }
        );
      }

      // Create new provider
      newProvider = await db.apiProvider.create({
        data: {
          name: selectedProvider,
          api_key: apiKey,
          api_url: apiUrl || '',
          login_user: username || null,
          login_pass: password || null,
          status: 'inactive'
        }
      });
    } catch (error) {
      console.error('Provider creation error:', error);
      return NextResponse.json(
        {
          error: 'Failed to create provider: ' + (error instanceof Error ? error.message : 'Unknown error'),
          success: false,
          data: null
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Provider ${providerConfig.label} added successfully`,
      data: {
        provider: {
          id: newProvider.id,
          name: newProvider.name,
          status: newProvider.status,
          ...providerConfig
        }
      },
      error: null
    });

  } catch (error) {
    console.error('Error adding provider:', error);
    return NextResponse.json(
      {
        error: 'Failed to add provider: ' + (error instanceof Error ? error.message : 'Unknown error'),
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

// PUT - Update provider status
export async function PUT(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        {
          error: 'Unauthorized access. Admin privileges required.',
          success: false,
          data: null
        },
        { status: 401 }
      );
    }

    const { id, status, apiKey, apiUrl, username, password } = await req.json();

    if (!id) {
      return NextResponse.json(
        {
          error: 'Provider ID is required',
          success: false,
          data: null
        },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    if (status !== undefined) updateData.status = status;
    if (apiKey !== undefined) updateData.api_key = apiKey;
    if (apiUrl !== undefined) updateData.api_url = apiUrl;
    if (username !== undefined) updateData.login_user = username || null;
    if (password !== undefined) updateData.login_pass = password || null;

    // If activating provider, validate required fields first
    if (status === 'active') {
      const provider = await db.apiProvider.findUnique({
        where: { id: parseInt(id) }
      });

      if (!provider) {
        return NextResponse.json(
          {
            error: 'Provider not found',
            success: false,
            data: null
          },
          { status: 404 }
        );
      }

      // Check if API URL and key will be available after update
      const finalApiUrl = updateData.api_url !== undefined ? updateData.api_url : provider.api_url;
      const finalApiKey = updateData.api_key !== undefined ? updateData.api_key : provider.api_key;

      if (!finalApiUrl || finalApiUrl.trim() === '') {
        return NextResponse.json(
          {
            error: 'Cannot activate provider: API URL is required',
            success: false,
            data: null
          },
          { status: 400 }
        );
      }

      if (!finalApiKey || finalApiKey.trim() === '') {
        return NextResponse.json(
          {
            error: 'Cannot activate provider: API Key is required',
            success: false,
            data: null
          },
          { status: 400 }
        );
      }

      // Validate URL format
      try {
        new URL(finalApiUrl);
      } catch (error) {
        return NextResponse.json(
          {
            error: 'Cannot activate provider: Invalid API URL format',
            success: false,
            data: null
          },
          { status: 400 }
        );
      }
    }

    // Update provider
    const updatedProvider = await db.apiProvider.update({
      where: { id: parseInt(id) },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      message: `Provider ${status === 'active' ? 'activated' : 'deactivated'} successfully`,
      data: {
        provider: updatedProvider
      },
      error: null
    });

  } catch (error) {
    console.error('Error updating provider:', error);
    return NextResponse.json(
      {
        error: 'Failed to update provider: ' + (error instanceof Error ? error.message : 'Unknown error'),
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

// DELETE - Delete provider
export async function DELETE(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        {
          error: 'Unauthorized access. Admin privileges required.',
          success: false,
          data: null
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        {
          error: 'Provider ID is required',
          success: false,
          data: null
        },
        { status: 400 }
      );
    }

    // Delete provider
    await db.apiProvider.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'Provider deleted successfully',
      data: null,
      error: null
    });

  } catch (error) {
    console.error('Error deleting provider:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete provider: ' + (error instanceof Error ? error.message : 'Unknown error'),
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}