// General Settings Model
model GeneralSettings {
  id         Int      @id @default(autoincrement())
  siteTitle  String   @default("SMM Panel")
  tagline    String   @default("Best SMM Services Provider")
  siteIcon   String   @default("")
  siteLogo   String   @default("")
  adminEmail String   @default("<EMAIL>")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("general_settings")
}

// Meta (SEO) Settings Model
model MetaSettings {
  id              Int      @id @default(autoincrement())
  googleTitle     String   @default("SMM Panel - Best Social Media Marketing Services")
  siteTitle       String   @default("SMM Panel")
  siteDescription String   @default("Get the best social media marketing services with fast delivery and affordable prices. Boost your social media presence today!") @db.Text
  keywords        String   @default("smm panel, social media marketing, instagram followers, youtube views, facebook likes") @db.Text
  thumbnail       String   @default("")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("meta_settings")
}

// User Settings Model
model UserSettings {
  id                      Int      @id @default(autoincrement())
  resetPasswordEnabled    Boolean  @default(true)
  signUpPageEnabled       Boolean  @default(true)
  nameFieldEnabled        Boolean  @default(true)
  emailConfirmationEnabled Boolean @default(true)
  resetLinkMax            Int      @default(3)
  transferFundsPercentage Int      @default(3)
  userFreeBalanceEnabled  Boolean  @default(false)
  freeAmount              Float    @default(0)
  paymentBonusEnabled     Boolean  @default(false)
  bonusPercentage         Float    @default(0)
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  @@map("user_settings")
}

// Ticket Settings Model
model TicketSettings {
  id                 Int             @id @default(autoincrement())
  ticketSystemEnabled Boolean        @default(true)
  maxPendingTickets  String          @default("3")
  subjects           TicketSubject[]
  createdAt          DateTime        @default(now())
  updatedAt          DateTime        @updatedAt

  @@map("ticket_settings")
}

// Ticket Subject Model
model TicketSubject {
  id               Int            @id @default(autoincrement())
  name             String
  ticketSettingsId Int
  ticketSettings   TicketSettings @relation(fields: [ticketSettingsId], references: [id], onDelete: Cascade)
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@map("ticket_subjects")
}

// Contact Settings Model
model ContactSettings {
  id                   Int               @id @default(autoincrement())
  contactSystemEnabled Boolean           @default(true)
  maxPendingContacts   String            @default("3")
  categories           ContactCategory[]
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt

  @@map("contact_settings")
}

// Contact Category Model
model ContactCategory {
  id                Int             @id @default(autoincrement())
  name              String
  contactSettingsId Int
  contactSettings   ContactSettings @relation(fields: [contactSettingsId], references: [id], onDelete: Cascade)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@map("contact_categories")
}

// Module Settings Model
model ModuleSettings {
  id                       Int      @id @default(autoincrement())
  // Affiliate Settings
  affiliateSystemEnabled   Boolean  @default(false)
  commissionRate           Float    @default(5)
  minimumPayout            Float    @default(10)
  // Child Panel Settings
  childPanelSellingEnabled Boolean  @default(false)
  childPanelPrice          Float    @default(10)
  // Other Module Settings
  serviceUpdateLogsEnabled Boolean  @default(true)
  massOrderEnabled         Boolean  @default(false)
  servicesListPublic       Boolean  @default(true)
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt

  @@map("module_settings")
}

// Service Update Logs Model
model ServiceUpdateLog {
  id            Int      @id @default(autoincrement())
  serviceId     Int
  serviceName   String
  adminId       Int
  adminEmail    String
  action        String   // 'created', 'updated', 'deleted', 'status_changed'
  changes       Json?    // Store the changes made
  oldValues     Json?    // Store old values for comparison
  newValues     Json?    // Store new values
  ipAddress     String?
  userAgent     String?
  createdAt     DateTime @default(now())

  @@map("service_update_logs")
}

// Affiliate System Models
model Affiliate {
  id              Int      @id @default(autoincrement())
  userId          Int      @unique
  referralCode    String   @unique
  commissionRate  Float    @default(5) // Percentage
  totalEarnings   Float    @default(0)
  availableEarnings Float  @default(0)
  totalReferrals  Int      @default(0)
  totalVisits     Int      @default(0)
  status          String   @default("active") // active, inactive, suspended
  paymentMethod   String?
  paymentDetails  Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  referrals       AffiliateReferral[]
  commissions     AffiliateCommission[]
  payouts         AffiliatePayout[]

  @@map("affiliates")
}

model AffiliateReferral {
  id          Int      @id @default(autoincrement())
  affiliateId Int
  referredUserId Int   @unique
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  // Relations
  affiliate   Affiliate @relation(fields: [affiliateId], references: [id], onDelete: Cascade)

  @@map("affiliate_referrals")
}

model AffiliateCommission {
  id          Int      @id @default(autoincrement())
  affiliateId Int
  referredUserId Int
  orderId     Int?
  amount      Float
  commissionRate Float
  commissionAmount Float
  status      String   @default("pending") // pending, approved, paid
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  affiliate   Affiliate @relation(fields: [affiliateId], references: [id], onDelete: Cascade)

  @@map("affiliate_commissions")
}

model AffiliatePayout {
  id          Int      @id @default(autoincrement())
  affiliateId Int
  amount      Float
  method      String
  details     Json?
  status      String   @default("pending") // pending, processing, completed, failed
  requestedAt DateTime @default(now())
  processedAt DateTime?
  notes       String?
  adminId     Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  affiliate   Affiliate @relation(fields: [affiliateId], references: [id], onDelete: Cascade)

  @@map("affiliate_payouts")
}

// Child Panel System Models
model ChildPanel {
  id              Int      @id @default(autoincrement())
  userId          Int      @unique
  domain          String   @unique
  subdomain       String?
  panelName       String
  apiKey          String   @unique
  status          String   @default("pending") // pending, active, inactive, suspended, expired
  plan            String   @default("basic") // basic, standard, premium
  theme           String   @default("default")
  customBranding  Boolean  @default(false)
  totalOrders     Int      @default(0)
  totalRevenue    Float    @default(0)
  apiCallsToday   Int      @default(0)
  apiCallsTotal   Int      @default(0)
  lastActivity    DateTime?
  expiryDate      DateTime?
  settings        Json?    // Store panel configuration
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  subscriptions   ChildPanelSubscription[]

  @@map("child_panels")
}

model ChildPanelSubscription {
  id            Int      @id @default(autoincrement())
  childPanelId  Int
  amount        Float
  currency      String   @default("USD")
  status        String   @default("pending") // pending, active, expired, cancelled
  startDate     DateTime @default(now())
  endDate       DateTime
  paymentMethod String?
  transactionId String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  childPanel    ChildPanel @relation(fields: [childPanelId], references: [id], onDelete: Cascade)

  @@map("child_panel_subscriptions")
}
