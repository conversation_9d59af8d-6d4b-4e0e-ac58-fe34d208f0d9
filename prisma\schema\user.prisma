model User {
  id                      Int                    @id @default(autoincrement())
  username                String?                @unique(map: "User_username_key")
  name                    String?
  password                String?
  email                   String?                @unique(map: "User_email_key")
  role                    Role                   @default(user)
  emailVerified           DateTime?
  image                   String?
  currency                String                 @default("USD")
  dollarRate              Float                  @default(121.45)
  balance                 Float                  @default(0)
  balanceUSD              Float                  @default(0) // Store balance in USD for conversion
  preferredCurrency       String                 @default("USD") // User's display preference
  total_deposit           Float                  @default(0) @db.Float
  total_spent             Float                  @default(0) @db.Float
  servicesDiscount        Float                  @default(0)
  specialPricing          Boolean                @default(false)
  status                  String                 @default("active")
  apiKey                  String?                @unique(map: "User_apiKey_key")
  isTwoFactorEnabled      Boolean                @default(false)
  language                String                 @default("en")
  timezone                String                 @default("21600")
  createdAt               DateTime               @default(now())
  updatedAt               DateTime               @updatedAt
  lastLoginAt             DateTime?
  accounts                Account?
  activityLogs            ActivityLog[]
  addFunds                AddFund[]
  apiKeys                 ApiKey[]
  cancelRequests          CancelRequest[]
  Category                Category[]
  favoriteServices        FavoriteService[]
  favouriteCat            FavrouteCat[]
  newOrders               NewOrder[]
  processedRefillRequests RefillRequest[]        @relation("ProcessedRefillRequests")
  refillRequests          RefillRequest[]
  services                Service[]
  sessions                Session[]
  twoFactorConfirmation   TwoFactorConfirmation?

  @@map("user")
}

model Account {
  id                       Int      @id @default(autoincrement())
  userId                   Int      @unique(map: "Account_userId_key")
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?  @db.Text
  access_token             String?  @db.Text
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?  @db.Text
  session_state            String?
  refresh_token_expires_in Int?
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
  user                     User     @relation(fields: [userId], references: [id])

  @@unique([provider, providerAccountId], map: "Account_provider_providerAccountId_key")
  @@index([userId], map: "Account_userId_idx")
  @@map("account")
}

model Session {
  id           Int      @id @default(autoincrement())
  sessionToken String   @unique(map: "Session_sessionToken_key")
  userId       Int
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id])

  @@index([userId], map: "Session_userId_idx")
  @@map("session")
}

model VerificationToken {
  id        Int      @id @default(autoincrement())
  token     String   @unique(map: "VerificationToken_token_key")
  expires   DateTime
  email     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([email, token], map: "VerificationToken_email_token_key")
  @@map("verificationtoken")
}

model PasswordResetToken {
  id        Int      @id @default(autoincrement())
  token     String   @unique(map: "PasswordResetToken_token_key")
  expires   DateTime
  email     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([email, token], map: "PasswordResetToken_email_token_key")
  @@map("passwordresettoken")
}

model TwoFactorToken {
  id        Int      @id @default(autoincrement())
  token     String   @unique(map: "TwoFactorToken_token_key")
  expires   DateTime
  email     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([email, token], map: "TwoFactorToken_email_token_key")
  @@map("twofactortoken")
}

model TwoFactorConfirmation {
  id        Int      @id @default(autoincrement())
  userId    Int      @unique(map: "TwoFactorConfirmation_userId_key")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("twofactorconfirmation")
}

model ApiKey {
  id        Int      @id @default(autoincrement())
  userId    Int
  key       String   @unique(map: "ApiKey_key_key")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User     @relation(fields: [userId], references: [id])

  @@index([userId], map: "ApiKey_userId_fkey")
  @@map("apikey")
}

enum Role {
  user
  admin
  moderator
}
